# encoding: utf-8
"""
sap_orchestrator – portable Variante
Alle temporären Dateien liegen im Unterordner TEMP des Basis-Ordners.
"""
import argparse
import subprocess
import sys
import time
from pathlib import Path
from typing import Optional, Tuple

# --------------------------------------------------------------------------
# Portable Basis-Pfad
# --------------------------------------------------------------------------
def get_base_dir() -> Path:
    if getattr(sys, "frozen", False):
        return Path(sys.executable).parent
    return Path(__file__).parent


BASE_DIR = get_base_dir()
TEMP_DIR = r"H:\TEMP"

# --------------------------------------------------------------------------
# SAP / Programm-Konstanten  (unverändert)
# --------------------------------------------------------------------------
SAP_EXECUTABLE_PATH = r"C:\Program Files (x86)\SAP\FrontEnd\SapGui\sapshcut.exe"
SAP_SYSTEM_ID = "TS4"
SAP_CLIENT = "009"
SAP_LANGUAGE = "DE"

LAGERORT_VON = "1090"
LAGERORT_NACH = "1010"
LAGERTYP = "957"
LAGERNUMMER_VON = "51B"
LAGERNUMMER_NACH = "512"
WERK = "5100"

EXPORT_FILE_NAME = "lsgitls24"
EXPORT_FILE_PATH = r"H:\TEMP"         # <-- jetzt dynamisch

CHARGEN_NR = ""

# --------------------------------------------------------------------------
def print_progress(step: int, total: int, message: str) -> None:
    percent = int(step * 100 / max(1, total))
    print(f"PROGRESS {percent} {message}")
    sys.stdout.flush()


def log_message(msg: str) -> None:
    print(msg)
    sys.stdout.flush()


def wait_for_element(session, element_id, timeout=30):
    """Wartet aktiv darauf, dass ein UI-Element in der SAP-Sitzung erscheint."""
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            element = session.findById(element_id)
            return element
        except Exception:
            time.sleep(0.5)
    raise TimeoutError(f"Element '{element_id}' wurde nach {timeout} Sekunden nicht gefunden.")


def get_sap_session():
    try:
        import win32com.client
    except ImportError:
        raise RuntimeError("pywin32 (win32com) wird benötigt für SAP GUI automation.")

    try:
        sap_gui_auto = win32com.client.GetObject("SAPGUI")
        application = sap_gui_auto.GetScriptingEngine
        if application.Children.Count > 0:
            connection = application.Children(0)
            if connection.Children.Count > 0:
                session = connection.Children(0)
                log_message("Bestehende SAP Session gefunden.")
                return session
    except Exception:
        log_message("Keine bestehende SAP Session gefunden.")

    start_sap_if_needed()
    time.sleep(5)

    try:
        sap_gui_auto = win32com.client.GetObject("SAPGUI")
        application = sap_gui_auto.GetScriptingEngine
        if application.Children.Count > 0:
            connection = application.Children(0)
            if connection.Children.Count > 0:
                session = connection.Children(0)
                log_message("SAP Session erfolgreich verbunden.")
                return session
    except Exception as e:
        raise RuntimeError(f"Konnte keine SAP Session erstellen: {e}")

    raise RuntimeError("SAP GUI Session konnte nicht erstellt werden.")


def start_sap_if_needed() -> None:
    try:
        args = [
            SAP_EXECUTABLE_PATH,
            f"-system={SAP_SYSTEM_ID}",
            f"-client={SAP_CLIENT}",
            f"-language={SAP_LANGUAGE}",
            "-reuse=1",
            "-maxgui"
        ]
        subprocess.Popen(args)
        log_message("Warte 8s, damit SAP GUI starten kann...")
        time.sleep(8)
    except FileNotFoundError:
        log_message("WARNUNG: sapshcut.exe nicht gefunden. Es wird angenommen, dass SAP bereits läuft.")


def close_excel() -> None:
    try:
        subprocess.run(["taskkill", "/F", "/IM", "excel.exe"], check=True, capture_output=True, text=True)
    except subprocess.CalledProcessError:
        pass


def close_sap_session(session) -> None:
    """Schließt nur das aktuelle SAP GUI Fenster, nicht das komplette SAP System."""
    try:
        # Versuche das Hauptfenster zu schließen
        session.findById("wnd[0]").close()
        log_message("SAP GUI Fenster geschlossen.")
    except Exception as e:
        log_message(f"Konnte SAP GUI Fenster nicht ordnungsgemäß schließen: {e}")
        try:
            # Alternative: Connection schließen
            connection = session.Parent
            connection.CloseSession(session.Id)
            log_message("SAP Session geschlossen.")
        except Exception as e2:
            log_message(f"Konnte SAP Session nicht schließen: {e2}")


# Hilfsfunktion für SAP-Zahlenformat
def format_sap_number(value):
    try:
        num = float(value)
        if num.is_integer():
            return str(int(num))  # Ganze Zahl ohne .0
        else:
            return str(num).replace(".", ",")  # Dezimal mit Komma
    except ValueError:
        return str(value)  # Falls kein Zahlwert, einfach als String zurückgeben


def execute_script1_llsgitls24(session) -> None:
    log_message("Führe Script 1 Logik aus: /LSGIT/LS24 Transaction")

    wait_for_element(session, "wnd[0]").maximize()
    session.findById("wnd[0]/tbar[0]/okcd").text = "/n/LSGIT/LS24"
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)

    wait_for_element(session, "wnd[0]/usr/ctxtS_LGNUM-LOW").text = LAGERNUMMER_VON
    wait_for_element(session, "wnd[0]/usr/ctxtS_WERKS-LOW").text = WERK
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)

    wait_for_element(session, "wnd[0]/usr/ctxtS_LGORT-LOW").text = LAGERORT_VON
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)

    wait_for_element(session, "wnd[0]/usr/txtS_CHARG-LOW").text = CHARGEN_NR
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)

    wait_for_element(session, "wnd[0]/tbar[1]/btn[8]").press()
    time.sleep(2)

    grid = wait_for_element(session, "wnd[0]/usr/cntlALV_CONTAINER_01/shellcont/shell")
    grid.contextMenu()
    grid.selectContextMenuItem("&XXL")
    time.sleep(2)

    wait_for_element(session, "wnd[1]/usr/ssubSUB_CONFIGURATION:SAPLSALV_GUI_CUL_EXPORT_AS:0512/txtGS_EXPORT-FILE_NAME").text = EXPORT_FILE_NAME
    session.findById("wnd[1]/tbar[0]/btn[20]").press()
    time.sleep(2)

    wait_for_element(session, "wnd[1]/usr/ctxtDY_PATH").text = EXPORT_FILE_PATH
    session.findById("wnd[1]/tbar[0]/btn[0]").press()
    time.sleep(5)

    log_message("Script 1 Logik abgeschlossen.")


def execute_script2_lt15(session, transportauftrag_nr: str) -> None:
    log_message("Führe Script 2 Logik aus: LT15 Transaction")

    wait_for_element(session, "wnd[0]/tbar[0]/okcd").text = "/nLT15"
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)

    wait_for_element(session, "wnd[0]/usr/txtLTAK-TANUM").text = transportauftrag_nr
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(2)

    wait_for_element(session, "wnd[0]/usr/txtLTAK-TANUM").caretPosition = 4
    session.findById("wnd[0]").sendVKey(0)
    session.findById("wnd[0]/tbar[0]/btn[11]").press()

    log_message("Script 2 Logik abgeschlossen.")


def execute_script3_migo(session, material_nr: str, gesamtbestand: str) -> None:
    log_message("Führe Script 3 Logik aus: MIGO Transaction")

    wait_for_element(session, "wnd[0]/tbar[0]/okcd").text = "/nMIGO"
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)

    wait_for_element(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-MAKTX[1,0]").text = material_nr
    
    bestand_formatiert = format_sap_number(gesamtbestand)
    wait_for_element(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/txtGOITEM-ERFMG[4,0]").text = bestand_formatiert
    
    wait_for_element(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-LGOBE[6,0]").text = LAGERORT_VON
    wait_for_element(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-NAME1[13,0]").text = WERK

    # ENTER nach Werk setzen
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)

    wait_for_element(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-CHARG[10,0]").text = CHARGEN_NR
    wait_for_element(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-UMLGOBE[32,0]").text = LAGERORT_NACH
    wait_for_element(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-UMCHA[36,0]").text = CHARGEN_NR

    time.sleep(1)

    wait_for_element(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-UMCHA[36,0]").setFocus()
    wait_for_element(session, "wnd[0]/usr/ssubSUB_MAIN_CARRIER:SAPLMIGO:0006/subSUB_ITEMLIST:SAPLMIGO:0200/tblSAPLMIGOTV_GOITEM/ctxtGOITEM-UMCHA[36,0]").caretPosition = 10
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(2)
    session.findById("wnd[0]/tbar[1]/btn[7]").press()
    time.sleep(1)
    # session.findById("wnd[1]/tbar[0]/btn[0]").press()
    # time.sleep(1)
    session.findById("wnd[0]/tbar[1]/btn[23]").press()
    time.sleep(2)

    log_message("Script 3 Logik abgeschlossen.")


def execute_script4_lt0651b(session) -> None:
    log_message("Führe Script 4 Logik aus: LT06 Transaction")

    wait_for_element(session, "wnd[0]/tbar[0]/okcd").text = "/nLT06"
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)
    
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)
    
    session.findById("wnd[0]/tbar[1]/btn[6]").press()
    time.sleep(1)
    
    wait_for_element(session, "wnd[0]/usr/ctxtLTAP-VLTYP").text = LAGERTYP
    time.sleep(1)
    wait_for_element(session, "wnd[0]/usr/ctxtLTAP-VLTYP").setFocus()
    time.sleep(1)
    wait_for_element(session, "wnd[0]/usr/ctxtLTAP-VLTYP").caretPosition = 3
    time.sleep(1)
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)
    
    session.findById("wnd[0]/tbar[0]/btn[11]").press()
    time.sleep(2)
    
    session.findById("wnd[0]/sbar").doubleClick()
    time.sleep(1)
    
    session.findById("wnd[0]/shellcont").close()
    time.sleep(2)

    log_message("Script 4 Logik abgeschlossen.")


def execute_script5_lt12(session) -> None:
    log_message("Führe Script 5 Logik aus: LT12 Transaction")

    wait_for_element(session, "wnd[0]/tbar[0]/okcd").text = "/nLT12"
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)

    wait_for_element(session, "wnd[0]/usr/chkRL03T-OFPOS").selected = True
    time.sleep(1)
    wait_for_element(session, "wnd[0]/usr/chkRLIST-SUBST").selected = True
    time.sleep(1)

    session.findById("wnd[0]").sendVKey(0)
    wait_for_element(session, "wnd[0]/tbar[0]/btn[11]").press()
    time.sleep(2)

    log_message("Script 5 Logik abgeschlossen.")


def execute_script6_lt06512(session) -> None:
    log_message("Führe Script 6 Logik aus: LT06 Transaction (Lagernummer nach)")

    wait_for_element(session, "wnd[0]/tbar[0]/okcd").text = "/nLT06"
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)

    wait_for_element(session, "wnd[0]/usr/ctxtRL02B-LGNUM").text = LAGERNUMMER_NACH
    time.sleep(1)
    wait_for_element(session, "wnd[0]/usr/ctxtRL02B-LGNUM").setFocus()
    time.sleep(1)
    wait_for_element(session, "wnd[0]/usr/ctxtRL02B-LGNUM").caretPosition = 3
    time.sleep(1)
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)
    
    session.findById("wnd[0]/tbar[1]/btn[6]").press()
    time.sleep(1)
    
    session.findById("wnd[0]/tbar[0]/btn[11]").press()
    time.sleep(1)
    
    wait_for_element(session, "wnd[0]/tbar[0]/okcd").text = "/nLT12"
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)
    
    wait_for_element(session, "wnd[0]/usr/chkRL03T-OFPOS").selected = True
    wait_for_element(session, "wnd[0]/usr/chkRLIST-SUBST").selected = True
    wait_for_element(session, "wnd[0]/usr/chkRLIST-SUBST").setFocus()
    session.findById("wnd[0]").sendVKey(0)
    time.sleep(1)
    
    session.findById("wnd[0]/tbar[0]/btn[11]").press()
    time.sleep(1)

    log_message("Script 6 Logik abgeschlossen.")


def find_export_file(folder: Path, base_name: str) -> Optional[Path]:
    xlsx = folder / f"{base_name}.xlsx"
    xls = folder / f"{base_name}.xls"
    if xlsx.exists():
        return xlsx
    if xls.exists():
        return xls
    candidates = sorted(folder.glob(f"{base_name}.*"), key=lambda p: p.stat().st_mtime, reverse=True)
    return candidates[0] if candidates else None


def read_cells_from_excel(file_path: Path) -> Tuple[str, str, str]:
    try:
        import win32com.client
    except Exception as e:
        raise RuntimeError("pywin32 (win32com) wird benötigt, um Excel-Dateien auszulesen.") from e

    excel = None
    try:
        excel = win32com.client.Dispatch("Excel.Application")
        excel.Visible = False
        wb = excel.Workbooks.Open(str(file_path))
        ws = wb.ActiveSheet
        val_u2 = ws.Range("U2").Value
        val_c2 = ws.Range("C2").Value
        val_ag2 = ws.Range("AG2").Value
        wb.Close(SaveChanges=False)
        return str(val_u2 or "").strip(), str(val_c2 or "").strip(), str(val_ag2 or "").strip()
    finally:
        if excel is not None:
            excel.Quit()


def main() -> None:
    parser = argparse.ArgumentParser(description="SAP Orchestrator")
    parser.add_argument("--charge", required=True)
    parser.add_argument("--dry-run", action="store_true")
    parser.add_argument("--start-step", type=int, default=1, choices=range(1, 7))
    parser.add_argument("--transportauftrag")
    parser.add_argument("--material")
    parser.add_argument("--bestand")
    parser.add_argument("--keep-sap-open", action="store_true")
    args = parser.parse_args()

    global CHARGEN_NR
    CHARGEN_NR = args.charge

    log_message(f"Charge: {CHARGEN_NR}")

    if args.dry_run:
        print_progress(10, 10, "Trockenlauf – beendet")
        return

    # Variablen für Excel-Daten
    transportauftrag_nr = args.transportauftrag or ""
    material_nr = args.material or ""
    gesamtbestand = args.bestand or ""

    print_progress(1, 10, "Verbinde mit SAP")
    session = get_sap_session()

    # Script 1
    if args.start_step <= 1:
        print_progress(2, 10, "Führe Script 1 (llsgitls24) aus")
        execute_script1_llsgitls24(session)
        close_excel()

        print_progress(3, 10, "Lese Export-Excel")
        export_dir = Path(TEMP_DIR)
        export_file = find_export_file(export_dir, EXPORT_FILE_NAME)
        if not export_file:
            raise FileNotFoundError(f"Export-Datei nicht gefunden in {TEMP_DIR}")
        transportauftrag_nr, material_nr, gesamtbestand = read_cells_from_excel(export_file)
        log_message(f"Excel-Daten gelesen: Transportauftrag={transportauftrag_nr}, Material={material_nr}, Bestand={gesamtbestand}")

    # Script 2
    if args.start_step <= 2:
        if not transportauftrag_nr:
            raise ValueError("Transportauftragsnummer fehlt. Verwende --transportauftrag oder starte bei Schritt 1.")
        print_progress(4, 10, "Führe Script 2 (lt15) aus")
        execute_script2_lt15(session, transportauftrag_nr)

    # Script 3
    if args.start_step <= 3:
        if not material_nr or not gesamtbestand:
            raise ValueError("Material oder Bestand fehlt. Verwende --material und --bestand oder starte bei Schritt 1.")
        print_progress(5, 10, "Führe Script 3 (MIGO) aus")
        execute_script3_migo(session, material_nr, gesamtbestand)

    # Script 4
    if args.start_step <= 4:
        print_progress(6, 10, "Führe Script 4 (lt0651b) aus")
        execute_script4_lt0651b(session)

    # Script 5
    if args.start_step <= 5:
        print_progress(7, 10, "Führe Script 5 (lt12) aus")
        execute_script5_lt12(session)

    # Script 6
    if args.start_step <= 6:
        print_progress(8, 10, "Führe Script 6 (lt06512) aus")
        execute_script6_lt06512(session)

    print_progress(9, 10, "Räume auf (Excel/SAP)")
    close_excel()
    
    # SAP GUI Fenster schließen (nur wenn nicht explizit offen gehalten werden soll)
    if not args.keep_sap_open:
        close_sap_session(session)

    print_progress(10, 10, "Fertig")


if __name__ == "__main__":
    main()